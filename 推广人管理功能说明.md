# 推广人管理功能说明

## 功能概述

为管理后台新增了推广人管理功能，包括推广人信息查看和推广记录查询功能。

## 后端接口

### 1. 现有接口
- `POST /api/userPromotion/page` - 分页查询所有推广人信息
- `GET /api/userPromotion/info` - 查询指定用户的推广信息
- `POST /api/userPromotion/withdraw/allRecords` - 查询所有提现记录

### 2. 新增接口
- `POST /api/userPromotion/promotionRecords` - 根据推广人查询推广记录
  - 参数：userToken (推广人用户标识), PageQueryReq (分页查询请求)
  - 返回：推广记录分页结果
  
- `GET /api/userPromotion/promotionCount` - 统计推广人的推广数量
  - 参数：userToken (推广人用户标识)
  - 返回：推广数量统计

## 前端页面

### 1. 推广人管理页面 (`promotionManage.vue`)

**路由路径：** `/promotionManage`

**功能特性：**
- 推广人列表展示（支持桌面端表格和移动端卡片视图）
- 搜索功能（按用户标识搜索）
- 分页显示
- 推广人详情查看
- 提现记录查看
- **新增：推广记录查看**

**数据展示：**
- 用户标识
- 推广金额
- 推广订单数
- 待提现金额
- 已提现金额
- 创建时间
- 更新时间

### 2. 推广记录功能

**触发方式：** 点击推广人列表中的"推广记录"按钮

**功能特性：**
- 显示推广人基本信息（用户标识、推广总数、推广金额）
- 推广记录列表展示：
  - 被推广用户
  - 推广奖励金额
  - 推广时间
  - 记录创建时间
- 支持分页查询
- 响应式设计，支持移动端

## 菜单配置

在后台管理菜单中添加了"推广人管理"菜单项，位于"推广设置"下方。

**菜单路径：** 系统设置 > 推广人管理

## 数据表关系

- `user_promotion` - 用户推广信息表
- `invitation_records` - 邀请记录表（推广记录）
- `withdraw_record` - 提现记录表

## 权限控制

所有推广人管理相关接口都需要管理员权限，通过 `checkIsAdmin()` 方法进行权限验证。

## 使用说明

1. **查看推广人列表**
   - 访问管理后台 > 系统设置 > 推广人管理
   - 可以搜索特定用户标识
   - 支持分页浏览

2. **查看推广记录**
   - 在推广人列表中点击"推广记录"按钮
   - 查看该推广人的所有推广记录
   - 支持分页查看推广记录

3. **查看详细信息**
   - 点击"详情"按钮查看推广人完整信息
   - 包括推广金额、订单数、提现情况等

4. **查看提现记录**
   - 点击"提现记录"按钮查看该推广人的提现历史
   - 包括提现金额、状态、时间等信息

## 技术实现

- **后端：** Spring Boot + MyBatis Plus
- **前端：** Vue 3 + Element Plus
- **响应式设计：** 支持桌面端和移动端
- **数据交互：** RESTful API + JSON

## 注意事项

1. 推广记录基于 `invitation_records` 表，通过 `inviter_name` 字段关联推广人
2. 所有金额显示保留两位小数
3. 时间格式使用本地化显示
4. 支持移动端响应式布局
5. 所有操作都有加载状态提示和错误处理
