
INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'enablePromotion', 'true', null);

INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'cashbackRate', '0.2', null);

INSERT INTO chatgpt_config (createTime, updateTime, deleted_at, `key`, value, remark)
VALUES ('2024-11-11 14:28:11.364', '2024-11-11 14:28:11.364', null, 'withdrawalThreshold', '20', null);
create table user_promotion(
                               id bigint auto_increment primary key,
                               user_token varchar(100) not null,
                               promotion_amount decimal(10,2) default 0 not null,
                               promotion_order_num int default 0 not null,
                               ##提现
                                   withdraw_amount decimal(10,2) default 0 not null,
                               ## 待提现
                                   wait_withdraw_amount decimal(10,2) default 0 not null,
                               created_at datetime default current_timestamp,
                               updated_at datetime default current_timestamp on update current_timestamp
) comment '用户推广表';

## 提现记录表
create table withdraw_record(
                                id bigint auto_increment primary key,
                                user_token varchar(100) not null,
                                amount decimal(10,2) not null,
                                status int not null comment '0-待处理，1-成功，2-失败',
                                remark varchar(255) comment '备注',
                                ##提现时间
                                    withdraw_time datetime,
                                ## 二维码图片
                                    qr_code_image varchar(255),
                                created_at datetime default current_timestamp,
                                updated_at datetime default current_timestamp on update current_timestamp
) comment '提现记录表';