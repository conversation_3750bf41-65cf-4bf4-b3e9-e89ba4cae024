<template>
    <div class="pagination-container">
        <!-- 工具栏部分 -->
        <el-row :gutter="10" class="toolbar">
            <el-col :xs="24" :sm="8" :md="6" :lg="4" class="toolbar-item">
                <el-input v-model="searchQuery.query" placeholder="请输入用户标识"></el-input>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button type="primary" @click="fetchData" :icon="Search">查询</el-button>
            </el-col>
            <el-col :xs="12" :sm="4" :md="3" :lg="2" class="toolbar-item">
                <el-button @click="resetSearch" :icon="Refresh">重置</el-button>
            </el-col>
        </el-row>

        <!-- 桌面端表格 -->
        <el-table v-if="!isMobile()" :data="tableData" style="width: 100%" v-loading="loading">
            <el-table-column prop="userToken" label="用户标识" min-width="120">
                <template #default="scope">
                    <el-text class="user-token">{{ scope.row.userToken }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="promotionAmount" label="推广金额" min-width="100" sortable>
                <template #default="scope">
                    <el-text type="success">¥{{ (scope.row.promotionAmount || 0).toFixed(2) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="promotionOrderNum" label="推广订单数" min-width="100" sortable>
                <template #default="scope">
                    <el-tag type="info">{{ scope.row.promotionOrderNum || 0 }}</el-tag>
                </template>
            </el-table-column>
            <el-table-column prop="waitWithdrawAmount" label="待提现金额" min-width="110" sortable>
                <template #default="scope">
                    <el-text type="warning">¥{{ (scope.row.waitWithdrawAmount || 0).toFixed(2) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="withdrawAmount" label="已提现金额" min-width="110" sortable>
                <template #default="scope">
                    <el-text type="primary">¥{{ (scope.row.withdrawAmount || 0).toFixed(2) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" min-width="160">
                <template #default="scope">
                    <el-text size="small">{{ formatDateTime(scope.row.createdAt) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column prop="updatedAt" label="更新时间" min-width="160">
                <template #default="scope">
                    <el-text size="small">{{ formatDateTime(scope.row.updatedAt) }}</el-text>
                </template>
            </el-table-column>
            <el-table-column label="操作" min-width="150" fixed="right">
                <template #default="scope">
                    <el-button type="primary" size="small" @click="viewDetails(scope.row)">
                        详情
                    </el-button>
                    <el-button type="success" size="small" @click="viewWithdrawRecords(scope.row)">
                        提现记录
                    </el-button>
                    <el-button type="info" size="small" @click="viewPromotionRecords(scope.row)">
                        推广记录
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 移动端卡片列表 -->
        <div v-else class="mobile-card-list">
            <el-card v-for="item in tableData" :key="item.id" class="mobile-card" shadow="hover">
                <div class="mobile-card-header">
                    <span class="mobile-card-title">{{ item.userToken }}</span>
                    <el-tag type="success">¥{{ (item.promotionAmount || 0).toFixed(2) }}</el-tag>
                </div>
                <div class="mobile-card-content">
                    <div class="mobile-card-item">
                        <span class="label">推广订单数:</span>
                        <span class="value">{{ item.promotionOrderNum || 0 }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">待提现:</span>
                        <span class="value text-warning">¥{{ (item.waitWithdrawAmount || 0).toFixed(2) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">已提现:</span>
                        <span class="value text-primary">¥{{ (item.withdrawAmount || 0).toFixed(2) }}</span>
                    </div>
                    <div class="mobile-card-item">
                        <span class="label">创建时间:</span>
                        <span class="value">{{ formatDateTime(item.createdAt) }}</span>
                    </div>
                </div>
                <div class="mobile-card-actions">
                    <el-button type="primary" size="small" @click="viewDetails(item)">详情</el-button>
                    <el-button type="success" size="small" @click="viewWithdrawRecords(item)">提现记录</el-button>
                    <el-button type="info" size="small" @click="viewPromotionRecords(item)">推广记录</el-button>
                </div>
            </el-card>
        </div>

        <!-- 分页组件 -->
        <el-pagination
            v-model:current-page="searchQuery.pageNum"
            v-model:page-size="searchQuery.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :small="isMobile()"
            :disabled="loading"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="pagination"
        />

        <!-- 详情对话框 -->
        <el-dialog v-model="detailDialogVisible" title="推广人详情" width="600px" :close-on-click-modal="false">
            <div v-if="currentPromoter" class="detail-content">
                <el-descriptions :column="2" border>
                    <el-descriptions-item label="用户标识">{{ currentPromoter.userToken }}</el-descriptions-item>
                    <el-descriptions-item label="推广金额">
                        <el-text type="success">¥{{ (currentPromoter.promotionAmount || 0).toFixed(2) }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="推广订单数">{{ currentPromoter.promotionOrderNum || 0 }}</el-descriptions-item>
                    <el-descriptions-item label="待提现金额">
                        <el-text type="warning">¥{{ (currentPromoter.waitWithdrawAmount || 0).toFixed(2) }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="已提现金额">
                        <el-text type="primary">¥{{ (currentPromoter.withdrawAmount || 0).toFixed(2) }}</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="提现率">
                        <el-text>{{ getWithdrawRate(currentPromoter) }}%</el-text>
                    </el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ formatDateTime(currentPromoter.createdAt) }}</el-descriptions-item>
                    <el-descriptions-item label="更新时间">{{ formatDateTime(currentPromoter.updatedAt) }}</el-descriptions-item>
                </el-descriptions>
            </div>
            <template #footer>
                <el-button @click="detailDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>

        <!-- 提现记录对话框 -->
        <el-dialog v-model="withdrawDialogVisible" title="提现记录" width="800px" :close-on-click-modal="false">
            <el-table :data="withdrawRecords" v-loading="withdrawLoading" style="width: 100%">
                <el-table-column prop="amount" label="提现金额" min-width="100">
                    <template #default="scope">
                        <el-text type="primary">¥{{ (scope.row.amount || 0).toFixed(2) }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" min-width="80">
                    <template #default="scope">
                        <el-tag :type="getWithdrawStatusType(scope.row.status)">
                            {{ getWithdrawStatusText(scope.row.status) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" min-width="120" show-overflow-tooltip />
                <el-table-column prop="withdrawTime" label="申请时间" min-width="160">
                    <template #default="scope">
                        <el-text size="small">{{ formatDateTime(scope.row.withdrawTime) }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="processTime" label="处理时间" min-width="160">
                    <template #default="scope">
                        <el-text size="small">{{ formatDateTime(scope.row.processTime) }}</el-text>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <el-button @click="withdrawDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>

        <!-- 推广记录对话框 -->
        <el-dialog v-model="promotionRecordsDialogVisible" title="推广记录" width="800px" :close-on-click-modal="false">
            <div v-if="currentPromoterForRecords" class="promotion-records-header">
                <el-descriptions :column="3" border size="small">
                    <el-descriptions-item label="推广人">{{ currentPromoterForRecords.userToken }}</el-descriptions-item>
                    <el-descriptions-item label="推广总数">{{ promotionRecordsTotal }}</el-descriptions-item>
                    <el-descriptions-item label="推广金额">
                        <el-text type="success">¥{{ (currentPromoterForRecords.promotionAmount || 0).toFixed(2) }}</el-text>
                    </el-descriptions-item>
                </el-descriptions>
            </div>
            <el-table :data="promotionRecords" v-loading="promotionRecordsLoading" style="width: 100%; margin-top: 15px;">
                <el-table-column prop="inviteeName" label="被推广用户" min-width="120">
                    <template #default="scope">
                        <el-text class="user-token">{{ scope.row.inviteeName }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="rewardNum" label="推广奖励" min-width="100">
                    <template #default="scope">
                        <el-text type="success">¥{{ (scope.row.rewardNum || 0).toFixed(2) }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="invitationTime" label="推广时间" min-width="160">
                    <template #default="scope">
                        <el-text size="small">{{ formatDateTime(scope.row.invitationTime) }}</el-text>
                    </template>
                </el-table-column>
                <el-table-column prop="createdAt" label="记录创建时间" min-width="160">
                    <template #default="scope">
                        <el-text size="small">{{ formatDateTime(scope.row.createdAt) }}</el-text>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 推广记录分页 -->
            <el-pagination
                v-if="promotionRecordsTotal > 0"
                v-model:current-page="promotionRecordsQuery.pageNum"
                v-model:page-size="promotionRecordsQuery.pageSize"
                :page-sizes="[10, 20, 50]"
                :small="isMobile()"
                :disabled="promotionRecordsLoading"
                :total="promotionRecordsTotal"
                layout="total, sizes, prev, pager, next"
                @size-change="handlePromotionRecordsSizeChange"
                @current-change="handlePromotionRecordsCurrentChange"
                style="margin-top: 15px; display: flex; justify-content: center;"
            />

            <template #footer>
                <el-button @click="promotionRecordsDialogVisible = false">关闭</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh } from '@element-plus/icons-vue'
import api from '@/axios'
import { isMobileUtil } from '@/utils'

// 响应式数据
const loading = ref(false)
const withdrawLoading = ref(false)
const promotionRecordsLoading = ref(false)
const tableData = ref([])
const total = ref(0)
const detailDialogVisible = ref(false)
const withdrawDialogVisible = ref(false)
const promotionRecordsDialogVisible = ref(false)
const currentPromoter = ref(null)
const currentPromoterForRecords = ref(null)
const withdrawRecords = ref([])
const promotionRecords = ref([])
const promotionRecordsTotal = ref(0)

// 搜索查询参数
const searchQuery = reactive({
    pageNum: 1,
    pageSize: 20,
    query: '',
    sortField: 'updated_at',
    sortOrder: 'desc'
})

// 推广记录查询参数
const promotionRecordsQuery = reactive({
    pageNum: 1,
    pageSize: 10,
    query: '',
    sortField: 'created_at',
    sortOrder: 'desc'
})

const { proxy } = getCurrentInstance()

// 获取数据
const fetchData = async () => {
    loading.value = true
    try {
        const res = await api.post('/api/userPromotion/page', searchQuery)
        if (res.data.code === 0) {
            tableData.value = res.data.data || []
            total.value = res.data.total || 0
        } else {
            ElMessage.error(res.data.msg || '获取推广人数据失败')
        }
    } catch (error) {
        console.error('获取推广人数据失败:', error)
        ElMessage.error('获取推广人数据失败')
    } finally {
        loading.value = false
    }
}

// 重置搜索
const resetSearch = () => {
    searchQuery.query = ''
    searchQuery.pageNum = 1
    fetchData()
}

// 分页处理
const handleSizeChange = (val) => {
    searchQuery.pageSize = val
    searchQuery.pageNum = 1
    fetchData()
}

const handleCurrentChange = (val) => {
    searchQuery.pageNum = val
    fetchData()
}

// 查看详情
const viewDetails = (row) => {
    currentPromoter.value = row
    detailDialogVisible.value = true
}

// 查看提现记录
const viewWithdrawRecords = async (row) => {
    withdrawLoading.value = true
    withdrawDialogVisible.value = true
    try {
        const res = await api.post('/api/userPromotion/withdraw/allRecords', {
            pageNum: 1,
            pageSize: 100,
            query: row.userToken
        })
        if (res.data.code === 0) {
            withdrawRecords.value = res.data.data || []
        } else {
            ElMessage.error(res.data.msg || '获取提现记录失败')
        }
    } catch (error) {
        console.error('获取提现记录失败:', error)
        ElMessage.error('获取提现记录失败')
    } finally {
        withdrawLoading.value = false
    }
}

// 查看推广记录
const viewPromotionRecords = async (row) => {
    currentPromoterForRecords.value = row
    promotionRecordsDialogVisible.value = true
    promotionRecordsQuery.pageNum = 1
    await fetchPromotionRecords(row.userToken)
}

// 获取推广记录
const fetchPromotionRecords = async (userToken) => {
    promotionRecordsLoading.value = true
    try {
        const res = await api.post(`/api/userPromotion/promotionRecords?userToken=${userToken}`, promotionRecordsQuery)
        if (res.data.code === 0) {
            promotionRecords.value = res.data.data || []
            promotionRecordsTotal.value = res.data.total || 0
        } else {
            ElMessage.error(res.data.msg || '获取推广记录失败')
        }
    } catch (error) {
        console.error('获取推广记录失败:', error)
        ElMessage.error('获取推广记录失败')
    } finally {
        promotionRecordsLoading.value = false
    }
}

// 推广记录分页处理
const handlePromotionRecordsSizeChange = (val) => {
    promotionRecordsQuery.pageSize = val
    promotionRecordsQuery.pageNum = 1
    if (currentPromoterForRecords.value) {
        fetchPromotionRecords(currentPromoterForRecords.value.userToken)
    }
}

const handlePromotionRecordsCurrentChange = (val) => {
    promotionRecordsQuery.pageNum = val
    if (currentPromoterForRecords.value) {
        fetchPromotionRecords(currentPromoterForRecords.value.userToken)
    }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
    if (!dateTime) return '-'
    return new Date(dateTime).toLocaleString('zh-CN')
}

// 计算提现率
const getWithdrawRate = (promoter) => {
    if (!promoter.promotionAmount || promoter.promotionAmount === 0) return '0'
    const rate = ((promoter.withdrawAmount || 0) / promoter.promotionAmount) * 100
    return rate.toFixed(1)
}

// 获取提现状态类型
const getWithdrawStatusType = (status) => {
    switch (status) {
        case 0: return 'warning'  // 待处理
        case 1: return 'success'  // 成功
        case 2: return 'danger'   // 失败
        default: return 'info'
    }
}

// 获取提现状态文本
const getWithdrawStatusText = (status) => {
    switch (status) {
        case 0: return '待处理'
        case 1: return '成功'
        case 2: return '失败'
        default: return '未知'
    }
}

// 初始化
onMounted(() => {
    fetchData()
})
</script>

<style scoped>
.pagination-container {
    padding: 20px;
}

.toolbar {
    margin-bottom: 20px;
}

.toolbar-item {
    margin-bottom: 10px;
}

.user-token {
    font-family: monospace;
    font-size: 12px;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;
}

/* 移动端样式 */
.mobile-card-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.mobile-card {
    margin-bottom: 0;
}

.mobile-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--el-border-color-light);
}

.mobile-card-title {
    font-weight: 600;
    font-size: 14px;
    font-family: monospace;
}

.mobile-card-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 12px;
}

.mobile-card-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.mobile-card-item .label {
    font-size: 13px;
    color: var(--el-text-color-regular);
}

.mobile-card-item .value {
    font-size: 13px;
    font-weight: 500;
}

.mobile-card-item .text-warning {
    color: var(--el-color-warning);
}

.mobile-card-item .text-primary {
    color: var(--el-color-primary);
}

.mobile-card-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.detail-content {
    padding: 10px 0;
}

.promotion-records-header {
    margin-bottom: 15px;
}

@media (max-width: 768px) {
    .pagination-container {
        padding: 10px;
    }
    
    .toolbar {
        margin-bottom: 15px;
    }
}
</style>
